"""
将涂鸦产品的已选中DP同步到Ezviz平台资源模板功能的命令。
"""
import json
import logging
import time

import click

from ledvance_cli.core import ezviz_api, tuya_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import snake_to_pascal

logger = logging.getLogger(__name__)


def _build_schema(dp: dict) -> dict:
    """根据涂鸦DP属性构建Ezviz功能schema。"""
    dp_type = dp['property']['type']
    schema = {}
    
    if dp_type == 'bool':
        schema = {
            'type': 'boolean',
        }
    elif dp_type == 'string' or dp_type == 'raw':
        schema = {
            'type': 'string',
            'maxLength': dp['property']['maxlen'],
        }
    elif dp_type == 'value':
        schema = {
            'type': 'integer',
            'minimum': dp['property']['min'],
            'maximum': dp['property']['max'],
        }
    elif dp_type == 'enum':
        schema = {
            'type': 'string',
            'maxLength': 250,
            'enumType': 'string',
            'enum': dp['property']['range'],
        }
    elif dp_type == 'bitmap':
        schema = {
            'type': 'string',
            'maxLength': 250,
            'enumType': 'string',
            'enum': dp['property']['label'],
        }
    
    return schema


def _build_feature_param(dp: dict, category: str, resource_template: dict) -> dict:
    """构建添加模板功能的参数。"""
    schema = _build_schema(dp)
    
    return {
        'catKey': category,
        'domainKey': 'global',
        'resId': resource_template['id'],
        'resKey': resource_template['category'],
        'propVo': {
            'id': 1,
            'access': 'rw' if dp['mode'] == 'rw' else 'r',
            'key': dp['code'],
            'name': json.dumps({
                'zh': dp['name'],
                'en': snake_to_pascal(dp['code']),
            }),
            'schema': json.dumps(schema),
            'did': '',
            'noPush': True,
            'ptype': 'STD',
            'publishVersion': 0,
            'remark': '',
            'sort': 0,
            'tplPublishId': 0,
            'tplPublishVersion': 0
        }
    }


@click.command("tuya-sync-dp")
@click.option("--env", default="Test", type=click.Choice(['Prod', 'Test'], case_sensitive=True), help="要操作的Ezviz平台环境。")
@click.option("--category", required=True, help="Ezviz平台的二级品类ID。")
@click.option("--pid", required=True, help="涂鸦产品ID。")
@click.pass_context
def tuya_sync_dp(ctx: click.Context, env: str, category: str, pid: str):
    """
    将涂鸦产品的已选中DP同步到Ezviz平台的资源模板功能。
    
    此命令会执行以下步骤：
    1. 获取Ezviz平台的资源模板
    2. 获取资源模板的现有功能列表
    3. 获取涂鸦产品的已选中DP列表
    4. 为每个DP构建schema并添加到资源模板功能
    5. 跳过已存在的重复DP code
    """
    is_dry_run = ctx.obj.dry_run

    logger.info(f"开始同步涂鸦产品 {pid} 的DP到Ezviz '{env}' 环境的品类 '{category}'...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式，将使用模拟数据替代实际API调用")

    try:
        # 1. 获取资源模板
        resource_template = ezviz_api.get_resource_template(category, env=env)
        if not resource_template:
            raise CLIError(f"无法获取品类 '{category}' 的资源模板。")
        
        logger.info(f"获取到资源模板: {resource_template['category']} (ID: {resource_template['id']})")

        # 2. 获取资源模板的现有功能
        features = ezviz_api.get_resource_template_features(resource_template['id'], env=env)
        if features is None:
            raise CLIError(f"无法获取资源模板 {resource_template['id']} 的功能列表。")
        
        logger.info(f"资源模板现有功能数量: {len(features)}")

        # 3. 获取涂鸦产品的DP
        dps = tuya_api.get_product_dp(pid)
        if not dps:
            raise CLIError(f"无法获取涂鸦产品 {pid} 的DP列表。")
        
        # 筛选已选中的DP
        selected_dps = [dp for dp in dps if dp.get("selected") == True]
        if len(selected_dps) == 0:
            logger.warning(f"涂鸦产品 {pid} 没有已选中的DP。")
            return
        
        logger.info(f"涂鸦产品 {pid} 共有 {len(selected_dps)} 个已选中的DP")

        # 4. 为每个已选中的DP添加模板功能
        success_count = 0
        skip_count = 0
        error_count = 0
        
        for dp in selected_dps:
            dp_code = dp['code']
            logger.info(f"处理DP: {dp_code}")
            
            # 检查是否已存在相同的DP code
            if any(item.get('key') == dp_code for item in features):
                logger.warning(f"DP code '{dp_code}' 已存在，跳过。")
                skip_count += 1
                continue
            
            try:
                # 构建功能参数
                feature_param = _build_feature_param(dp, category, resource_template)
                
                # 添加模板功能
                result = ezviz_api.add_template_feature(feature_param, env=env)
                if result:
                    logger.info(f"成功添加模板功能: {dp_code}")
                    success_count += 1
                else:
                    logger.error(f"添加模板功能失败: {dp_code}")
                    error_count += 1
                
                # 添加延迟以避免请求过于频繁
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"处理DP '{dp_code}' 时发生错误: {e}")
                error_count += 1
                continue

        # 5. 输出统计结果
        logger.info("=" * 50)
        logger.info("同步完成统计:")
        logger.info(f"  成功添加: {success_count} 个DP")
        logger.info(f"  跳过重复: {skip_count} 个DP")
        logger.info(f"  处理失败: {error_count} 个DP")
        logger.info(f"  总计处理: {len(selected_dps)} 个DP")
        logger.info("=" * 50)

    except CLIError as e:
        logger.error(f"同步失败: {e}")
        return
    except Exception as e:
        logger.error(f"发生意外错误: {e}")
        return
